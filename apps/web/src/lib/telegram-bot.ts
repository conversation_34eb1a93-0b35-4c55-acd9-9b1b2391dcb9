/**
 * Telegram Bot Service
 * 
 * Core Telegram bot implementation that integrates with Benji AI agent
 */

import TelegramBot from 'node-telegram-bot-api';
import { prisma } from './db-utils';
import { getBenjiForUser } from './benji-agent';
import { getTelegramBenjiForUser, TelegramBenjiAgent } from './telegram-benji-agent';
// import { checkRateLimit, recordUsage } from './db-utils'; // Using enhanced Telegram versions instead
import { FeatureType } from '../../prisma/generated/index.js';
import { 
  validateMessage, 
  validateTwitterUrl, 
  parseCommand, 
  sanitizeText, 
  sanitizeCallbackData,
  shouldRejectBasedOnContent,
  hasSecurityRisk
} from './telegram-validation';
import {
  checkTelegramUserRateLimit,
  checkTelegramSpam,
  checkTelegramFeatureLimit,
  recordTelegramFeatureUsage
} from './telegram-rate-limiting';

export interface TelegramBotConfig {
  token: string;
  webhookUrl?: string;
  enablePolling?: boolean;
}

export class TelegramBotService {
  private bot: TelegramBot;
  private config: TelegramBotConfig;

  constructor(config: TelegramBotConfig) {
    this.config = config;
    this.bot = new TelegramBot(config.token, {
      polling: config.enablePolling || false,
      webHook: !config.enablePolling
    });

    this.setupEventHandlers();
  }

  /**
   * Set up webhook for production use
   */
  async setWebhook(url: string) {
    try {
      await this.bot.setWebHook(url);
      console.log('✅ Telegram webhook set successfully:', url);
    } catch (error) {
      console.error('❌ Failed to set Telegram webhook:', error);
      throw error;
    }
  }

  /**
   * Process incoming webhook updates with comprehensive validation
   */
  async processUpdate(update: any) {
    try {
      console.log('📨 Telegram: Processing update:', {
        updateId: update.update_id,
        messageId: update.message?.message_id,
        chatId: update.message?.chat?.id,
        from: update.message?.from?.username
      });

      // Validate message if present
      if (update.message) {
        const validation = validateMessage(update.message);
        if (!validation.isValid) {
          console.warn('⚠️ Telegram: Invalid message:', validation.errors);
          return; // Silently ignore invalid messages
        }
        
        // Use sanitized message
        update.message = validation.sanitizedMessage;
        
        // Security checks
        if (update.message.text) {
          const securityCheck = hasSecurityRisk(update.message.text);
          if (securityCheck.hasRisk) {
            console.warn('🚨 Telegram: Security risk detected:', securityCheck.risks);
            await this.bot.sendMessage(
              update.message.chat.id,
              '⚠️ Your message contains potentially unsafe content and cannot be processed.'
            );
            return;
          }

          const contentCheck = shouldRejectBasedOnContent(update.message.text);
          if (contentCheck.shouldReject) {
            console.warn('🚫 Telegram: Content rejected:', contentCheck.reason);
            await this.bot.sendMessage(
              update.message.chat.id,
              '⚠️ Your message appears to be spam or contains too many links/mentions.'
            );
            return;
          }

          // Check Telegram-specific rate limits and spam
          const userId = update.message.from?.id.toString();
          if (userId) {
            const rateLimitCheck = await checkTelegramUserRateLimit(userId, 'message');
            if (!rateLimitCheck.allowed) {
              console.warn('⚠️ Telegram: Rate limit exceeded:', rateLimitCheck.reason);
              if (rateLimitCheck.isSpam) {
                await this.bot.sendMessage(
                  update.message.chat.id,
                  '🚫 Anti-spam protection activated. Please wait before sending more messages.'
                );
              } else {
                await this.bot.sendMessage(
                  update.message.chat.id,
                  `⚠️ Rate limit exceeded. Please wait ${Math.ceil((rateLimitCheck.resetTime! - Date.now()) / 1000)} seconds.`
                );
              }
              return;
            }

            const spamCheck = checkTelegramSpam(userId, update.message.text);
            if (spamCheck.isSpam) {
              console.warn('🚫 Telegram: Spam detected:', spamCheck.reason);
              await this.bot.sendMessage(
                update.message.chat.id,
                '🚫 Spam detected. Please avoid repeating messages or sending too many messages quickly.'
              );
              return;
            }
          }
        }
      }

      // Validate callback query if present
      if (update.callback_query) {
        if (update.callback_query.data) {
          const sanitizedData = sanitizeCallbackData(update.callback_query.data);
          if (sanitizedData !== update.callback_query.data) {
            console.warn('⚠️ Telegram: Callback data sanitized');
            update.callback_query.data = sanitizedData;
          }
        }
      }

      this.bot.processUpdate(update);
    } catch (error) {
      console.error('❌ Telegram: Error processing update:', error);
      throw error;
    }
  }

  /**
   * Set up event handlers for bot interactions
   */
  private setupEventHandlers() {
    // Handle /start command
    this.bot.onText(/\/start/, async (msg) => {
      await this.handleStartCommand(msg);
    });

    // Handle /help command
    this.bot.onText(/\/help/, async (msg) => {
      await this.handleHelpCommand(msg);
    });

    // Handle /settings command
    this.bot.onText(/\/settings/, async (msg) => {
      await this.handleSettingsCommand(msg);
    });

    // Handle /status command
    this.bot.onText(/\/status/, async (msg) => {
      await this.handleStatusCommand(msg);
    });

    // Handle Twitter/X URLs
    this.bot.onText(/https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/, async (msg) => {
      await this.handleTwitterUrl(msg);
    });

    // Handle general messages
    this.bot.on('message', async (msg) => {
      // Skip if it's a command (already handled above)
      if (msg.text?.startsWith('/')) return;
      
      await this.handleGeneralMessage(msg);
    });

    // Handle callback queries (inline keyboard responses)
    this.bot.on('callback_query', async (query) => {
      await this.handleCallbackQuery(query);
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('❌ Telegram Bot Error:', error);
    });
  }

  /**
   * Handle /start command
   */
  private async handleStartCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    try {
      console.log('🚀 Telegram: Handling /start command for user:', telegramUser.username);

      // Get or create Telegram user record
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      const welcomeMessage = `
🤖 *Welcome to BuddyChip AI!*

I'm Benji, your AI assistant with access to powerful tools and capabilities.

*What I can do:*
• 🐦 Analyze Twitter/X links and generate smart replies
• 🔍 Search the web for real-time information
• 🎨 Generate images with AI
• 💬 Have intelligent conversations
• 📊 Provide crypto market insights

*Getting Started:*
${dbTelegramUser.userId ? 
  '✅ Your account is already linked! You can start using all features.' : 
  '🔗 Link your BuddyChip account with /settings to unlock all features.'
}

*Commands:*
/help - Show all available commands
/settings - Manage your account and preferences
/status - Check your usage and limits

*Try me:*
• Send me a Twitter/X link for instant AI replies
• Ask me anything - I'll search and provide answers
• Request image generation with detailed prompts

Ready to get started? 🚀
      `;

      await this.bot.sendMessage(chatId, welcomeMessage, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔗 Link Account', callback_data: 'link_account' },
              { text: '📚 Help', callback_data: 'help' }
            ]
          ]
        }
      });

    } catch (error) {
      console.error('❌ Telegram: Error in /start command:', error);
      await this.bot.sendMessage(chatId, '❌ Sorry, something went wrong. Please try again.');
    }
  }

  /**
   * Handle /help command
   */
  private async handleHelpCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;

    const helpMessage = `
📚 *BuddyChip AI Help*

*Available Commands:*
/start - Welcome message and setup
/help - Show this help message
/settings - Account settings and preferences
/status - Check usage limits and account status

*Features:*
🐦 *Twitter Integration*
• Send any Twitter/X link
• Get instant AI-generated replies
• Perfect for social media engagement

🔍 *Web Search*
• Ask questions about current events
• Get real-time information
• Powered by advanced search tools

🎨 *Image Generation*
• Request AI-generated images
• Use detailed prompts for best results
• High-quality DALL-E 3 powered

💬 *Conversations*
• Natural language interactions
• Context-aware responses
• Multi-turn conversations

*Tips:*
• Be specific in your requests
• Use detailed prompts for images
• Check /status for usage limits
• Link your account for full access

Need more help? Contact support through the BuddyChip dashboard.
    `;

    await this.bot.sendMessage(chatId, helpMessage, {
      parse_mode: 'Markdown'
    });
  }

  /**
   * Handle /settings command
   */
  private async handleSettingsCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    try {
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);
      
      const settingsMessage = `
⚙️ *Account Settings*

*Account Status:*
${dbTelegramUser.userId ? 
  '✅ Linked to BuddyChip account' : 
  '❌ Not linked - limited functionality'
}

*Current Settings:*
• Language: ${telegramUser.language_code || 'en'}
• Active: ${dbTelegramUser.isActive ? '✅' : '❌'}

${!dbTelegramUser.userId ? 
  '*To unlock all features:*\n1. Visit buddychip.app\n2. Create an account\n3. Use the link below to connect' : 
  '*Manage your subscription and preferences at buddychip.app*'
}
      `;

      const keyboard = dbTelegramUser.userId ? 
        [
          [{ text: '🌐 Open Dashboard', url: 'https://buddychip.app/dashboard' }],
          [{ text: '🔄 Refresh Status', callback_data: 'refresh_status' }]
        ] : 
        [
          [{ text: '🔗 Link Account', callback_data: 'link_account' }],
          [{ text: '🌐 Create Account', url: 'https://buddychip.app' }]
        ];

      await this.bot.sendMessage(chatId, settingsMessage, {
        parse_mode: 'Markdown',
        reply_markup: { inline_keyboard: keyboard }
      });

    } catch (error) {
      console.error('❌ Telegram: Error in /settings command:', error);
      await this.bot.sendMessage(chatId, '❌ Error loading settings. Please try again.');
    }
  }

  /**
   * Handle /status command
   */
  private async handleStatusCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    try {
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.bot.sendMessage(chatId, 
          '❌ Account not linked. Use /settings to link your BuddyChip account for usage tracking.',
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Get user's plan and usage information
      const user = await prisma.user.findUnique({
        where: { id: dbTelegramUser.userId },
        include: { 
          plan: {
            include: {
              features: true
            }
          }
        }
      });

      if (!user) {
        await this.bot.sendMessage(chatId, '❌ User account not found.');
        return;
      }

      // Get feature limits from plan features
      const aiCallsFeature = user.plan.features.find(f => f.feature === 'AI_CALLS');
      const imageGenFeature = user.plan.features.find(f => f.feature === 'IMAGE_GENERATIONS');

      const aiCallsLimit = aiCallsFeature?.limit ?? 0;
      const imageGenLimit = imageGenFeature?.limit ?? 0;

      const statusMessage = `
📊 *Account Status*

*Plan:* ${user.plan.displayName}
*Status:* ${user.plan.isActive ? '✅ Active' : '❌ Inactive'}

*Usage Limits:*
• AI Calls: ${aiCallsLimit === -1 ? 'Unlimited' : aiCallsLimit + '/month'}
• Image Generation: ${imageGenLimit === -1 ? 'Unlimited' : imageGenLimit + '/month'}

*Account Info:*
• Member since: ${user.createdAt.toLocaleDateString()}
• Last active: ${user.lastActiveAt?.toLocaleDateString() || 'Never'}

Visit buddychip.app for detailed usage analytics.
      `;

      await this.bot.sendMessage(chatId, statusMessage, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [{ text: '🌐 Open Dashboard', url: 'https://buddychip.app/dashboard' }]
          ]
        }
      });

    } catch (error) {
      console.error('❌ Telegram: Error in /status command:', error);
      await this.bot.sendMessage(chatId, '❌ Error loading status. Please try again.');
    }
  }

  /**
   * Get or create Telegram user record
   */
  private async getOrCreateTelegramUser(telegramUser: TelegramBot.User) {
    const telegramId = telegramUser.id.toString();

    let dbTelegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId }
    });

    if (!dbTelegramUser) {
      dbTelegramUser = await prisma.telegramUser.create({
        data: {
          telegramId,
          username: telegramUser.username,
          firstName: telegramUser.first_name,
          lastName: telegramUser.last_name,
          languageCode: telegramUser.language_code,
          lastActiveAt: new Date()
        }
      });
      console.log('✅ Telegram: Created new user record:', telegramId);
    } else {
      // Update last active time and user info
      dbTelegramUser = await prisma.telegramUser.update({
        where: { id: dbTelegramUser.id },
        data: {
          username: telegramUser.username,
          firstName: telegramUser.first_name,
          lastName: telegramUser.last_name,
          languageCode: telegramUser.language_code,
          lastActiveAt: new Date()
        }
      });
    }

    return dbTelegramUser;
  }

  /**
   * Handle Twitter/X URLs
   */
  private async handleTwitterUrl(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;
    const twitterUrl = msg.text;

    if (!telegramUser || !twitterUrl) return;

    try {
      console.log('🐦 Telegram: Processing Twitter URL:', twitterUrl);

      // Validate Twitter URL
      const urlValidation = validateTwitterUrl(twitterUrl);
      if (!urlValidation.isValid) {
        console.error('❌ Telegram: Invalid Twitter URL:', urlValidation.error);
        await this.bot.sendMessage(chatId,
          `❌ Invalid Twitter URL: ${urlValidation.error}`
        );
        return;
      }

      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.bot.sendMessage(chatId,
          '🔗 Please link your BuddyChip account first using /settings to use Twitter analysis features.',
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Check Telegram rate limits first
      const telegramRateLimit = await checkTelegramUserRateLimit(telegramUser.id.toString(), 'twitter_url');
      if (!telegramRateLimit.allowed) {
        await this.bot.sendMessage(chatId,
          `⚠️ Too many Twitter URL requests. Please wait ${Math.ceil((telegramRateLimit.resetTime! - Date.now()) / 1000)} seconds.`
        );
        return;
      }

      // Check subscription feature limits
      const featureLimit = await checkTelegramFeatureLimit(dbTelegramUser.userId, FeatureType.AI_CALLS, 1);
      if (!featureLimit.allowed) {
        await this.bot.sendMessage(chatId,
          `⚠️ AI calls limit exceeded. ${featureLimit.remaining} remaining this month.`
        );
        return;
      }

      // Send "typing" indicator
      await this.bot.sendChatAction(chatId, 'typing');

      // Get Telegram Benji agent for the user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Extract tweet content (simplified - in production you'd use Twitter API)
      const tweetContent = await this.extractTweetContent(twitterUrl);

      if (!tweetContent) {
        await this.bot.sendMessage(chatId, '❌ Could not extract tweet content. Please check the URL.');
        return;
      }

      // Generate AI response optimized for Telegram
      const result = await benji.generateTelegramQuickReply(tweetContent, twitterUrl);

      // Convert streaming result to text
      let responseText = '';
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      // Record usage
      await recordTelegramFeatureUsage(dbTelegramUser.userId, FeatureType.AI_CALLS, 1, {
        tweetUrl: twitterUrl,
        messageType: 'telegram-quick-reply',
        telegramUserId: dbTelegramUser.telegramId,
        chatId: chatId.toString()
      });

      // Format response for Telegram
      const formattedResponse = `
🐦 *AI Reply Generated*

*Original Tweet:*
"${tweetContent.substring(0, 200)}${tweetContent.length > 200 ? '...' : ''}"

*Suggested Reply:*
${responseText}

*Actions:*
      `;

      await this.bot.sendMessage(chatId, formattedResponse, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔄 Regenerate', callback_data: `regenerate:${Buffer.from(twitterUrl).toString('base64')}` },
              { text: '✨ Enhance', callback_data: `enhance:${Buffer.from(twitterUrl).toString('base64')}` }
            ],
            [
              { text: '📋 Copy Reply', callback_data: `copy:${Buffer.from(responseText).toString('base64')}` }
            ]
          ]
        }
      });

    } catch (error) {
      console.error('❌ Telegram: Error processing Twitter URL:', error);
      await this.bot.sendMessage(chatId, '❌ Error processing Twitter link. Please try again.');
    }
  }

  /**
   * Handle general messages (AI conversation)
   */
  private async handleGeneralMessage(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;
    const messageText = msg.text;

    if (!telegramUser || !messageText) return;

    try {
      console.log('💬 Telegram: Processing general message from:', telegramUser.username);

      // Sanitize message text
      const sanitizedText = sanitizeText(messageText);
      if (!sanitizedText) {
        console.warn('⚠️ Telegram: Empty message after sanitization');
        await this.bot.sendMessage(chatId,
          '⚠️ Your message cannot be processed. Please send a valid text message.'
        );
        return;
      }

      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.bot.sendMessage(chatId,
          '🔗 Please link your BuddyChip account using /settings to unlock AI conversation features.\n\nYou can still use basic commands like /help and /start.',
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Check subscription feature limits
      const featureLimit = await checkTelegramFeatureLimit(dbTelegramUser.userId, FeatureType.AI_CALLS, 1);
      if (!featureLimit.allowed) {
        await this.bot.sendMessage(chatId,
          `⚠️ AI calls limit exceeded. ${featureLimit.remaining} remaining this month.`
        );
        return;
      }

      // Send "typing" indicator
      await this.bot.sendChatAction(chatId, 'typing');

      // Get Telegram Benji agent for the user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Generate AI response optimized for Telegram
      const result = await benji.generateTelegramResponse(sanitizedText, {
        telegramUserId: dbTelegramUser.id,
        telegramChatId: chatId.toString()
      });

      // Convert streaming result to text
      let responseText = '';
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      // Record usage
      await recordTelegramFeatureUsage(dbTelegramUser.userId, FeatureType.AI_CALLS, 1, {
        messageType: 'telegram-conversation',
        telegramUserId: dbTelegramUser.telegramId,
        chatId: chatId.toString(),
        messageLength: sanitizedText.length
      });

      // Split long messages for Telegram's 4096 character limit
      const messages = benji.formatForTelegram(responseText);

      for (const message of messages) {
        await this.bot.sendMessage(chatId, message, {
          parse_mode: 'Markdown',
          disable_web_page_preview: true
        });
      }

    } catch (error) {
      console.error('❌ Telegram: Error processing general message:', error);
      await this.bot.sendMessage(chatId, '❌ Sorry, I encountered an error. Please try again.');
    }
  }

  /**
   * Handle callback queries from inline keyboards
   */
  private async handleCallbackQuery(query: TelegramBot.CallbackQuery) {
    const chatId = query.message?.chat.id;
    const data = query.data;

    if (!chatId || !data) return;

    try {
      console.log('🔘 Telegram: Processing callback query:', data);

      // Answer the callback query to remove loading state
      await this.bot.answerCallbackQuery(query.id);

      if (data === 'link_account') {
        await this.handleLinkAccount(chatId);
      } else if (data === 'help') {
        await this.handleHelpCommand({ chat: { id: chatId } } as TelegramBot.Message);
      } else if (data === 'refresh_status') {
        await this.handleStatusCommand({ chat: { id: chatId }, from: query.from } as TelegramBot.Message);
      } else if (data.startsWith('regenerate:')) {
        await this.handleRegenerateReply(chatId, data);
      } else if (data.startsWith('enhance:')) {
        await this.handleEnhanceReply(chatId, data);
      } else if (data.startsWith('copy:')) {
        await this.handleCopyReply(chatId, data);
      }

    } catch (error) {
      console.error('❌ Telegram: Error processing callback query:', error);
      await this.bot.sendMessage(chatId, '❌ Error processing request. Please try again.');
    }
  }

  /**
   * Handle account linking
   */
  private async handleLinkAccount(chatId: number) {
    const linkMessage = `
🔗 *Link Your BuddyChip Account*

To unlock all AI features, you need to link your Telegram account with BuddyChip:

*Steps:*
1. Visit buddychip.app and create an account (if you don't have one)
2. Go to Settings → Telegram Integration
3. Click "Link Telegram Account"
4. Use the code below:

*Your Link Code:* \`TG_${chatId}_${Date.now()}\`

This code expires in 10 minutes for security.

*Why link your account?*
• Access to all AI tools and features
• Usage tracking and limits
• Personalized AI responses
• Premium model access (based on your plan)
    `;

    await this.bot.sendMessage(chatId, linkMessage, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '🌐 Open BuddyChip', url: 'https://buddychip.app' }]
        ]
      }
    });
  }

  /**
   * Extract tweet content from URL using existing Twitter API client
   */
  private async extractTweetContent(url: string): Promise<string | null> {
    try {
      console.log('🔍 Telegram: Extracting tweet content from:', url);

      // Import and use the existing Twitter API client
      const { twitterClient } = await import('./twitter-client');
      
      // Validate the URL first
      if (!twitterClient.validateTwitterUrl(url)) {
        console.error('❌ Telegram: Invalid Twitter URL format');
        return null;
      }

      // Extract tweet content using the existing API client
      const tweet = await twitterClient.getTweetFromUrl(url);
      
      if (!tweet) {
        console.error('❌ Telegram: Could not fetch tweet content');
        return null;
      }

      console.log('✅ Telegram: Successfully extracted tweet content:', {
        tweetId: tweet.id,
        author: tweet.author.userName,
        textLength: tweet.text.length
      });

      return tweet.text;

    } catch (error) {
      console.error('❌ Telegram: Error extracting tweet content:', error);
      return null;
    }
  }

  /**
   * Split long messages to fit Telegram's character limit
   */
  private splitLongMessage(text: string, maxLength: number = 4000): string[] {
    if (text.length <= maxLength) {
      return [text];
    }

    const messages: string[] = [];
    let currentMessage = '';

    const lines = text.split('\n');

    for (const line of lines) {
      if ((currentMessage + line + '\n').length > maxLength) {
        if (currentMessage) {
          messages.push(currentMessage.trim());
          currentMessage = '';
        }

        // If a single line is too long, split it by words
        if (line.length > maxLength) {
          const words = line.split(' ');
          for (const word of words) {
            if ((currentMessage + word + ' ').length > maxLength) {
              if (currentMessage) {
                messages.push(currentMessage.trim());
                currentMessage = '';
              }
            }
            currentMessage += word + ' ';
          }
        } else {
          currentMessage = line + '\n';
        }
      } else {
        currentMessage += line + '\n';
      }
    }

    if (currentMessage.trim()) {
      messages.push(currentMessage.trim());
    }

    return messages;
  }

  /**
   * Handle regenerate reply callback
   */
  private async handleRegenerateReply(chatId: number, data: string) {
    try {
      const urlBase64 = data.split(':')[1];
      const twitterUrl = Buffer.from(urlBase64, 'base64').toString();

      await this.bot.sendMessage(chatId, '🔄 Regenerating reply...');

      // Simulate the Twitter URL message to reprocess
      await this.handleTwitterUrl({
        chat: { id: chatId },
        text: twitterUrl,
        from: { id: chatId } // Simplified - in real implementation you'd get the actual user
      } as any);

    } catch (error) {
      console.error('❌ Telegram: Error regenerating reply:', error);
      await this.bot.sendMessage(chatId, '❌ Error regenerating reply. Please try again.');
    }
  }

  /**
   * Handle enhance reply callback
   */
  private async handleEnhanceReply(chatId: number, data: string) {
    try {
      const urlBase64 = data.split(':')[1];
      const twitterUrl = Buffer.from(urlBase64, 'base64').toString();

      await this.bot.sendMessage(chatId, '✨ Generating enhanced reply with o3 model...');

      // This would integrate with the enhanced response functionality
      // For now, show a placeholder message
      await this.bot.sendMessage(chatId,
        '✨ Enhanced replies coming soon! This will use the OpenAI o3 model for highest quality responses.',
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('❌ Telegram: Error enhancing reply:', error);
      await this.bot.sendMessage(chatId, '❌ Error enhancing reply. Please try again.');
    }
  }

  /**
   * Handle copy reply callback
   */
  private async handleCopyReply(chatId: number, data: string) {
    try {
      const replyBase64 = data.split(':')[1];
      const replyText = Buffer.from(replyBase64, 'base64').toString();

      await this.bot.sendMessage(chatId,
        `📋 *Reply copied!*\n\n\`${replyText}\`\n\nYou can now paste this reply on Twitter/X.`,
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('❌ Telegram: Error copying reply:', error);
      await this.bot.sendMessage(chatId, '❌ Error copying reply. Please try again.');
    }
  }
}
