/**
 * Telegram-Specific Rate Limiting and Anti-Spam Protection
 * 
 * Enhanced rate limiting specifically for Telegram bot operations
 */

import { checkRateLimit, recordUsage } from './db-utils';
import { FeatureType } from '../../prisma/generated/index.js';

// Telegram-specific rate limiting rules
export const TELEGRAM_RATE_LIMITS = {
  // Per-user limits (per minute)
  MESSAGES_PER_MINUTE: 20,
  COMMANDS_PER_MINUTE: 10,
  TWITTER_URLS_PER_MINUTE: 5,
  CALLBACK_QUERIES_PER_MINUTE: 30,
  
  // Per-chat limits (per minute)
  CHAT_MESSAGES_PER_MINUTE: 60,
  
  // Burst protection (short-term limits)
  MESSAGES_PER_10_SECONDS: 5,
  IDENTICAL_MESSAGES_THRESHOLD: 3, // Same message repeated
  
  // Anti-spam thresholds
  MAX_MESSAGE_LENGTH: 4000,
  MAX_URLS_PER_MESSAGE: 3,
  MAX_MENTIONS_PER_MESSAGE: 5,
  
  // Cool-down periods (in milliseconds)
  SPAM_COOLDOWN: 60000, // 1 minute
  RATE_LIMIT_COOLDOWN: 30000, // 30 seconds
} as const;

// In-memory rate limiting store
interface RateLimitEntry {
  count: number;
  firstRequest: number;
  lastRequest: number;
  windowMs: number;
}

interface AntiSpamEntry {
  lastMessages: string[];
  spamDetectedAt?: number;
  warningCount: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();
const antiSpamStore = new Map<string, AntiSpamEntry>();

/**
 * Clean up expired entries from rate limit store
 */
function cleanupRateLimitStore() {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now - entry.firstRequest > entry.windowMs) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Clean up expired entries from anti-spam store
 */
function cleanupAntiSpamStore() {
  const now = Date.now();
  for (const [key, entry] of antiSpamStore.entries()) {
    // Remove spam detection after cooldown period
    if (entry.spamDetectedAt && now - entry.spamDetectedAt > TELEGRAM_RATE_LIMITS.SPAM_COOLDOWN) {
      entry.spamDetectedAt = undefined;
      entry.warningCount = 0;
    }
    
    // Keep only last 10 messages and those from last 5 minutes
    entry.lastMessages = entry.lastMessages
      .slice(-10)
      .filter(msg => now - parseInt(msg.split(':')[0]) < 5 * 60 * 1000);
    
    // Remove entry if no recent activity
    if (entry.lastMessages.length === 0 && !entry.spamDetectedAt) {
      antiSpamStore.delete(key);
    }
  }
}

/**
 * Check rate limit for a specific key
 */
function checkGenericRateLimit(
  key: string, 
  limit: number, 
  windowMs: number
): { allowed: boolean; remaining: number; resetTime: number } {
  cleanupRateLimitStore();
  
  const now = Date.now();
  const entry = rateLimitStore.get(key);
  
  if (!entry) {
    // First request
    rateLimitStore.set(key, {
      count: 1,
      firstRequest: now,
      lastRequest: now,
      windowMs
    });
    return { allowed: true, remaining: limit - 1, resetTime: now + windowMs };
  }
  
  // Check if window has expired
  if (now - entry.firstRequest >= windowMs) {
    // Reset window
    rateLimitStore.set(key, {
      count: 1,
      firstRequest: now,
      lastRequest: now,
      windowMs
    });
    return { allowed: true, remaining: limit - 1, resetTime: now + windowMs };
  }
  
  // Check if limit exceeded
  if (entry.count >= limit) {
    return { 
      allowed: false, 
      remaining: 0, 
      resetTime: entry.firstRequest + windowMs 
    };
  }
  
  // Increment counter
  entry.count++;
  entry.lastRequest = now;
  
  return { 
    allowed: true, 
    remaining: limit - entry.count, 
    resetTime: entry.firstRequest + windowMs 
  };
}

/**
 * Check if user is currently in spam cooldown
 */
function isInSpamCooldown(userId: string): boolean {
  const entry = antiSpamStore.get(userId);
  if (!entry?.spamDetectedAt) return false;
  
  const now = Date.now();
  return now - entry.spamDetectedAt < TELEGRAM_RATE_LIMITS.SPAM_COOLDOWN;
}

/**
 * Detect spam patterns in user messages
 */
function detectSpam(userId: string, messageText: string): { isSpam: boolean; reason?: string } {
  cleanupAntiSpamStore();
  
  const now = Date.now();
  const timestamp = now.toString();
  const messageEntry = `${timestamp}:${messageText}`;
  
  let entry = antiSpamStore.get(userId);
  if (!entry) {
    entry = { lastMessages: [], warningCount: 0 };
    antiSpamStore.set(userId, entry);
  }
  
  // Add current message
  entry.lastMessages.push(messageEntry);
  
  // Check for identical messages
  const recentMessages = entry.lastMessages
    .filter(msg => now - parseInt(msg.split(':')[0]) < 60000) // Last minute
    .map(msg => msg.split(':').slice(1).join(':'));
  
  const identicalCount = recentMessages.filter(msg => msg === messageText).length;
  
  if (identicalCount >= TELEGRAM_RATE_LIMITS.IDENTICAL_MESSAGES_THRESHOLD) {
    entry.spamDetectedAt = now;
    entry.warningCount++;
    return { isSpam: true, reason: 'Identical messages repeated' };
  }
  
  // Check for rapid fire messages (same content pattern)
  const rapidMessages = entry.lastMessages
    .filter(msg => now - parseInt(msg.split(':')[0]) < 10000) // Last 10 seconds
    .map(msg => msg.split(':').slice(1).join(':'));
  
  if (rapidMessages.length > TELEGRAM_RATE_LIMITS.MESSAGES_PER_10_SECONDS) {
    entry.spamDetectedAt = now;
    entry.warningCount++;
    return { isSpam: true, reason: 'Too many messages in short time' };
  }
  
  return { isSpam: false };
}

/**
 * Check Telegram user rate limits
 */
export async function checkTelegramUserRateLimit(
  userId: string,
  action: 'message' | 'command' | 'twitter_url' | 'callback_query'
): Promise<{ 
  allowed: boolean; 
  reason?: string; 
  remaining?: number; 
  resetTime?: number;
  isSpam?: boolean;
}> {
  try {
    // Check if user is in spam cooldown
    if (isInSpamCooldown(userId)) {
      return { 
        allowed: false, 
        reason: 'User in spam cooldown', 
        isSpam: true 
      };
    }

    // Get rate limit config for action
    const config = {
      message: { limit: TELEGRAM_RATE_LIMITS.MESSAGES_PER_MINUTE, window: 60000 },
      command: { limit: TELEGRAM_RATE_LIMITS.COMMANDS_PER_MINUTE, window: 60000 },
      twitter_url: { limit: TELEGRAM_RATE_LIMITS.TWITTER_URLS_PER_MINUTE, window: 60000 },
      callback_query: { limit: TELEGRAM_RATE_LIMITS.CALLBACK_QUERIES_PER_MINUTE, window: 60000 },
    }[action];

    // Check rate limit
    const key = `telegram:${action}:${userId}`;
    const result = checkGenericRateLimit(key, config.limit, config.window);
    
    if (!result.allowed) {
      return {
        allowed: false,
        reason: `Rate limit exceeded for ${action}`,
        remaining: result.remaining,
        resetTime: result.resetTime
      };
    }

    return {
      allowed: true,
      remaining: result.remaining,
      resetTime: result.resetTime
    };
  } catch (error) {
    console.error('❌ Telegram Rate Limit: Error checking rate limit:', error);
    // Fail open - allow the request if there's an error
    return { allowed: true };
  }
}

/**
 * Check spam for message content
 */
export function checkTelegramSpam(
  userId: string,
  messageText: string
): { isSpam: boolean; reason?: string } {
  try {
    return detectSpam(userId, messageText);
  } catch (error) {
    console.error('❌ Telegram Spam Check: Error checking spam:', error);
    // Fail open - don't block if there's an error
    return { isSpam: false };
  }
}

/**
 * Check subscription-based feature limits (integrates with existing rate limiting)
 */
export async function checkTelegramFeatureLimit(
  userId: string,
  feature: FeatureType,
  amount: number = 1
): Promise<{
  allowed: boolean;
  currentUsage: number;
  limit: number;
  remaining: number;
  planName?: string;
}> {
  try {
    const result = await checkRateLimit(userId, feature, amount);
    return result;
  } catch (error) {
    console.error('❌ Telegram Feature Limit: Error checking feature limit:', error);
    // Fail closed for subscription features - deny if there's an error
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      remaining: 0
    };
  }
}

/**
 * Record feature usage for Telegram operations
 */
export async function recordTelegramFeatureUsage(
  userId: string,
  feature: FeatureType,
  amount: number = 1,
  telegramMetadata?: {
    telegramUserId?: string;
    chatId?: string;
    messageType?: string;
    [key: string]: any;
  }
): Promise<void> {
  try {
    await recordUsage(userId, feature, amount, {
      platform: 'telegram',
      ...telegramMetadata
    });
  } catch (error) {
    console.error('❌ Telegram Usage Recording: Error recording usage:', error);
    // Don't throw - usage recording failure shouldn't block the operation
  }
}

/**
 * Get user's current rate limit status for Telegram
 */
export function getTelegramRateLimitStatus(userId: string): {
  isInCooldown: boolean;
  rateLimits: Array<{
    action: string;
    remaining: number;
    resetTime: number;
  }>;
} {
  const actions = ['message', 'command', 'twitter_url', 'callback_query'] as const;
  const rateLimits: Array<{ action: string; remaining: number; resetTime: number }> = [];
  
  for (const action of actions) {
    const key = `telegram:${action}:${userId}`;
    const entry = rateLimitStore.get(key);
    
    if (entry) {
      const config = {
        message: { limit: TELEGRAM_RATE_LIMITS.MESSAGES_PER_MINUTE, window: 60000 },
        command: { limit: TELEGRAM_RATE_LIMITS.COMMANDS_PER_MINUTE, window: 60000 },
        twitter_url: { limit: TELEGRAM_RATE_LIMITS.TWITTER_URLS_PER_MINUTE, window: 60000 },
        callback_query: { limit: TELEGRAM_RATE_LIMITS.CALLBACK_QUERIES_PER_MINUTE, window: 60000 },
      }[action];
      
      rateLimits.push({
        action,
        remaining: Math.max(0, config.limit - entry.count),
        resetTime: entry.firstRequest + entry.windowMs
      });
    }
  }
  
  return {
    isInCooldown: isInSpamCooldown(userId),
    rateLimits
  };
}

/**
 * Reset rate limits for a user (admin function)
 */
export function resetTelegramRateLimits(userId: string): void {
  const keysToDelete: string[] = [];
  
  for (const key of rateLimitStore.keys()) {
    if (key.includes(userId)) {
      keysToDelete.push(key);
    }
  }
  
  keysToDelete.forEach(key => rateLimitStore.delete(key));
  antiSpamStore.delete(userId);
  
  console.log(`🔄 Telegram Rate Limits: Reset limits for user ${userId}`);
}

/**
 * Get rate limiting statistics (for monitoring)
 */
export function getTelegramRateLimitStats(): {
  activeUsers: number;
  totalRateLimitEntries: number;
  totalAntiSpamEntries: number;
  spamCooldownUsers: number;
} {
  cleanupRateLimitStore();
  cleanupAntiSpamStore();
  
  const activeUsers = new Set<string>();
  for (const key of rateLimitStore.keys()) {
    const userId = key.split(':')[2];
    if (userId) activeUsers.add(userId);
  }
  
  const spamCooldownUsers = Array.from(antiSpamStore.values())
    .filter(entry => entry.spamDetectedAt && 
      Date.now() - entry.spamDetectedAt < TELEGRAM_RATE_LIMITS.SPAM_COOLDOWN
    ).length;
  
  return {
    activeUsers: activeUsers.size,
    totalRateLimitEntries: rateLimitStore.size,
    totalAntiSpamEntries: antiSpamStore.size,
    spamCooldownUsers
  };
}

// Auto-cleanup interval (run every 5 minutes)
setInterval(() => {
  cleanupRateLimitStore();
  cleanupAntiSpamStore();
}, 5 * 60 * 1000);