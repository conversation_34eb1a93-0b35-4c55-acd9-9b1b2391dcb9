/**
 * Telegram Bot Startup
 * 
 * Automatically initializes and starts the Telegram bot in development mode
 */

import { TelegramBotService } from './telegram-bot';

let botService: any = null;
let isInitialized = false;

/**
 * Start the Telegram bot service
 */
export async function startTelegramBot() {
  // Prevent multiple initializations
  if (isInitialized) {
    console.log('🤖 Telegram: Bot already initialized');
    return botService;
  }

  try {
    console.log('🚀 Telegram: Starting bot service...');

    // Check if we have the required token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.warn('⚠️ Telegram: TELEGRAM_BOT_TOKEN not configured - bot will not start');
      return null;
    }

    // Create bot service directly with development settings
    const isDevelopment = !process.env.NODE_ENV || process.env.NODE_ENV === 'development';

    botService = new TelegramBotService({
      token: process.env.TELEGRAM_BOT_TOKEN,
      enablePolling: isDevelopment,
      webhookUrl: !isDevelopment
        ? `${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/webhook`
        : undefined
    });
    
    if (botService) {
      isInitialized = true;
      console.log('✅ Telegram: Bot service started successfully');
      
      // In development, the bot uses polling and should start listening immediately
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Telegram: Development mode - bot is now listening for messages via polling');
      } else {
        console.log('🌐 Telegram: Production mode - bot will use webhooks');
      }
    } else {
      console.error('❌ Telegram: Failed to initialize bot service');
    }

    return botService;
    
  } catch (error) {
    console.error('❌ Telegram: Error starting bot service:', error);
    return null;
  }
}

/**
 * Get the current bot service instance
 */
export function getBotService() {
  return botService;
}

/**
 * Check if the bot is initialized
 */
export function isBotInitialized() {
  return isInitialized;
}

/**
 * Auto-start the bot in development mode
 */
async function autoStartBot() {
  const isDevelopment = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
  const isServer = typeof window === 'undefined';

  if (isDevelopment && isServer && process.env.TELEGRAM_BOT_TOKEN) {
    console.log('🔧 Telegram: Auto-starting bot in development mode...');
    await startTelegramBot();
  }
}

// Try to auto-start immediately
autoStartBot();

// Also try after a short delay in case environment variables load later
setTimeout(() => autoStartBot(), 100);
