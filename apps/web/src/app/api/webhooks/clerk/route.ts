import type { WebhookEvent } from "@clerk/nextjs/server";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { Webhook } from "svix";
import { prisma } from "@/lib/db-utils";

// Type definitions for Clerk webhook data
interface ClerkEmailAddress {
  id: string;
  email_address: string;
}

interface ClerkUserData {
  id: string;
  email_addresses: <PERSON><PERSON>mailAddress[];
  first_name: string | null;
  last_name: string | null;
  image_url: string | null;
  primary_email_address_id: string | null;
}

interface ClerkDeletedUserData {
  id: string;
}

interface ClerkSubscriptionData {
  object: string;
  id: string;
  status: string;
  user_id: string;
  plan: string;
  // Add other subscription fields as needed
  [key: string]: unknown;
}

interface ClerkPaymentData {
  object: string;
  id: string;
  user_id: string;
  amount: number;
  currency: string;
  reason?: string;
  // Add other payment fields as needed
  [key: string]: unknown;
}

// Type for billing webhook events (using intersection type instead of extension)
type BillingWebhookEvent = WebhookEvent & {
  data: ClerkSubscriptionData | ClerkPaymentData;
};

export async function POST(req: Request) {
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SIGNING_SECRET;

  if (!WEBHOOK_SECRET) {
    throw new Error(
      "Missing CLERK_WEBHOOK_SIGNING_SECRET environment variable"
    );
  }

  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occurred -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.text();
  const body = JSON.parse(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occurred", {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;
  console.log(`Received Clerk webhook: ${eventType}`);

  try {
    switch (eventType) {
      case "user.created":
        await handleUserCreated(evt.data);
        break;
      case "user.updated":
        await handleUserUpdated(evt.data);
        break;
      case "user.deleted":
        await handleUserDeleted(evt.data as ClerkDeletedUserData);
        break;
      default: {
        // Handle billing events with string matching since they're not in the type definition yet
        const eventTypeStr = eventType as string;
        const billingEvt = evt as BillingWebhookEvent;
        if (eventTypeStr === "subscription.created") {
          await handleSubscriptionCreated(billingEvt.data as ClerkSubscriptionData);
        } else if (eventTypeStr === "subscription.updated") {
          await handleSubscriptionUpdated(billingEvt.data as ClerkSubscriptionData);
        } else if (eventTypeStr === "subscription.deleted") {
          await handleSubscriptionDeleted(billingEvt.data as ClerkSubscriptionData);
        } else if (eventTypeStr === "invoice.payment_succeeded") {
          await handlePaymentSucceeded(billingEvt.data as ClerkPaymentData);
        } else if (eventTypeStr === "invoice.payment_failed") {
          await handlePaymentFailed(billingEvt.data as ClerkPaymentData);
        } else {
          console.log(`Unhandled webhook event: ${eventType}`);
        }
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error handling webhook ${eventType}:`, error);
    return new Response("Webhook handler failed", { status: 500 });
  }
}

async function handleUserCreated(userData: ClerkUserData) {
  const { id, email_addresses, first_name, last_name, image_url } = userData;

  // Get primary email
  const primaryEmail = email_addresses?.find(
    (email: ClerkEmailAddress) => email.id === userData.primary_email_address_id
  ) || email_addresses?.[0]; // Fallback to first email if no primary

  // Get default plan (Reply Guy)
  const defaultPlan = await prisma.subscriptionPlan.findFirst({
    where: { name: "reply-guy" },
  });

  if (!defaultPlan) {
    throw new Error("Default subscription plan not found");
  }

  // Create user in our database
  const user = await prisma.user.create({
    data: {
      id, // Use Clerk's user ID
      email: primaryEmail?.email_address || null,
      name:
        first_name && last_name
          ? `${first_name} ${last_name}`
          : first_name || null,
      avatar: image_url || null,
      planId: defaultPlan.id,
      lastActiveAt: new Date(),
    },
  });

  console.log(`Created user in database: ${user.id}`);
}

async function handleUserUpdated(userData: ClerkUserData) {
  const { id, email_addresses, first_name, last_name, image_url } = userData;

  // Get primary email
  const primaryEmail = email_addresses?.find(
    (email: ClerkEmailAddress) => email.id === userData.primary_email_address_id
  ) || email_addresses?.[0]; // Fallback to first email if no primary

  // Update user in our database
  await prisma.user.update({
    where: { id },
    data: {
      email: primaryEmail?.email_address || null,
      name:
        first_name && last_name
          ? `${first_name} ${last_name}`
          : first_name || null,
      avatar: image_url || null,
      lastActiveAt: new Date(),
    },
  });

  console.log(`Updated user in database: ${id}`);
}

async function handleUserDeleted(userData: ClerkDeletedUserData) {
  const { id } = userData;

  // Delete user and all related data (cascade will handle relationships)
  await prisma.user.delete({
    where: { id },
  });

  console.log(`Deleted user from database: ${id}`);
}

// Billing Event Handlers
async function handleSubscriptionCreated(subscriptionData: ClerkSubscriptionData) {
  console.log("🎉 Subscription created:", subscriptionData);

  const { user_id, plan, status, id } = subscriptionData;

  try {
    // Update user's plan in database
    await prisma.user.update({
      where: { id: user_id },
      data: {
        clerkPlanId: id,
        clerkPlanName: plan,
        subscriptionStatus: status,
        subscriptionUpdatedAt: new Date(),
      },
    });

    console.log(
      `✅ Updated user ${user_id} subscription to plan: ${plan}`
    );
  } catch (error) {
    console.error("❌ Error updating user subscription:", error);
    throw error;
  }
}

async function handleSubscriptionUpdated(subscriptionData: ClerkSubscriptionData) {
  console.log("🔄 Subscription updated:", subscriptionData);

  const { user_id, plan, status, id } = subscriptionData;

  try {
    // Update user's plan in database
    await prisma.user.update({
      where: { id: user_id },
      data: {
        clerkPlanId: id,
        clerkPlanName: plan,
        subscriptionStatus: status,
        subscriptionUpdatedAt: new Date(),
      },
    });

    console.log(
      `✅ Updated user ${user_id} subscription to plan: ${plan}`
    );
  } catch (error) {
    console.error("❌ Error updating user subscription:", error);
    throw error;
  }
}

async function handleSubscriptionDeleted(subscriptionData: ClerkSubscriptionData) {
  console.log("🗑️ Subscription deleted:", subscriptionData);

  const { user_id } = subscriptionData;

  try {
    // Revert user to default plan
    const defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: "reply-guy" },
    });

    if (defaultPlan) {
      await prisma.user.update({
        where: { id: user_id },
        data: {
          planId: defaultPlan.id,
          clerkPlanId: null,
          clerkPlanName: null,
          subscriptionStatus: "inactive",
          subscriptionUpdatedAt: new Date(),
        },
      });
    }

    console.log(`✅ Reverted user ${user_id} to default plan`);
  } catch (error) {
    console.error("❌ Error reverting user subscription:", error);
    throw error;
  }
}

async function handlePaymentSucceeded(paymentData: ClerkPaymentData) {
  console.log("💰 Payment succeeded:", paymentData);

  const { user_id, amount, currency } = paymentData;

  try {
    // Log successful payment
    console.log(
      `✅ Payment of ${amount} ${currency} succeeded for user: ${user_id}`
    );

    // You can add additional logic here like:
    // - Sending confirmation emails
    // - Updating payment history
    // - Triggering analytics events
  } catch (error) {
    console.error("❌ Error handling payment success:", error);
    throw error;
  }
}

async function handlePaymentFailed(paymentData: ClerkPaymentData) {
  console.log("❌ Payment failed:", paymentData);

  const { user_id, amount, currency, reason } = paymentData;

  try {
    // Log failed payment
    console.log(
      `❌ Payment of ${amount} ${currency} failed for user: ${user_id}. Reason: ${reason}`
    );

    // You can add additional logic here like:
    // - Sending notification emails
    // - Updating subscription status
    // - Triggering retry logic
  } catch (error) {
    console.error("❌ Error handling payment failure:", error);
    throw error;
  }
}
