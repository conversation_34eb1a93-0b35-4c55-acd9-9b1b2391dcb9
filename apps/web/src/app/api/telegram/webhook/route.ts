/**
 * Telegram Webhook Handler
 * 
 * Processes incoming Telegram bot updates via webhook with production-grade security and error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { TelegramBotService } from '@/lib/telegram-bot';
import { securityMiddleware, TelegramSecurityMonitor } from '@/lib/telegram-security';
import { z } from 'zod';

// Telegram Update validation schema
const TelegramUpdateSchema = z.object({
  update_id: z.number(),
  message: z.object({
    message_id: z.number(),
    chat: z.object({
      id: z.number(),
      type: z.enum(['private', 'group', 'supergroup', 'channel'])
    }),
    from: z.object({
      id: z.number(),
      username: z.string().optional(),
      first_name: z.string().optional(),
      last_name: z.string().optional(),
      language_code: z.string().optional(),
      is_bot: z.boolean().optional()
    }).optional(),
    text: z.string().max(4096).optional(), // Telegram's message length limit
    date: z.number()
  }).optional(),
  callback_query: z.object({
    id: z.string(),
    from: z.object({
      id: z.number(),
      username: z.string().optional(),
      first_name: z.string().optional(),
      is_bot: z.boolean().optional()
    }),
    message: z.object({
      chat: z.object({
        id: z.number()
      })
    }).optional(),
    data: z.string().max(64).optional() // Telegram's callback data limit
  }).optional()
}).refine(
  (data) => data.message || data.callback_query,
  { message: "Update must contain either message or callback_query" }
);

// Rate limiting for webhook requests
const requestCounts = new Map<string, { count: number; resetTime: number }>();

function checkWebhookRateLimit(identifier: string): boolean {
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 100; // Max 100 requests per minute per identifier
  
  const key = `webhook:${identifier}`;
  const current = requestCounts.get(key);
  
  if (!current || now > current.resetTime) {
    requestCounts.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}

// Initialize bot service (singleton pattern)
let botService: TelegramBotService | null = null;

function getBotService(): TelegramBotService {
  if (!botService) {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    if (!token) {
      throw new Error('TELEGRAM_BOT_TOKEN environment variable is required');
    }

    botService = new TelegramBotService({
      token,
      enablePolling: false // Use webhooks in production
    });
  }
  return botService;
}

/**
 * Handle incoming Telegram webhook updates
 */
export async function POST(req: NextRequest) {
  const startTime = Date.now();
  let updateId: number | undefined;
  const monitor = TelegramSecurityMonitor.getInstance();
  
  try {
    // Get client IP for security and logging
    const clientIP = req.headers.get('x-forwarded-for')?.split(',')[0] || 
                    req.headers.get('x-real-ip') || 
                    'unknown';

    console.log('📨 Telegram Webhook: Received update from IP:', clientIP);

    // Verify environment variables
    const botToken = process.env.TELEGRAM_BOT_TOKEN;

    if (!botToken) {
      console.error('❌ Telegram Webhook: Missing TELEGRAM_BOT_TOKEN');
      return NextResponse.json(
        { error: 'Service unavailable' },
        { status: 503 }
      );
    }

    // Get request body
    const body = await req.text();

    // Convert headers to plain object for security middleware
    const headers: Record<string, string | undefined> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // Perform comprehensive security check
    const securityResult = securityMiddleware(
      {
        ip: clientIP,
        headers,
        body
      },
      botToken
    );

    if (!securityResult.allowed) {
      console.error('🚨 Telegram Webhook: Security check failed:', securityResult.securityInfo);
      return NextResponse.json(
        securityResult.response?.body || { error: 'Security check failed' },
        { status: securityResult.response?.status || 403 }
      );
    }

    // Log security warnings
    if (securityResult.securityInfo.warnings?.length > 0) {
      console.warn('⚠️ Telegram Webhook: Security warnings:', securityResult.securityInfo.warnings);
    }

    // Additional webhook-specific rate limiting
    if (!checkWebhookRateLimit(clientIP)) {
      console.warn('⚠️ Telegram Webhook: Rate limit exceeded for IP:', clientIP);
      monitor.recordSecurityEvent('WEBHOOK_RATE_LIMIT', { ip: clientIP });
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    // Parse and validate JSON (already checked in security middleware but parse again)
    let rawUpdate;
    try {
      rawUpdate = JSON.parse(body);
    } catch (error) {
      console.error('❌ Telegram Webhook: Invalid JSON from IP:', clientIP, error);
      monitor.recordSecurityEvent('INVALID_JSON', { ip: clientIP, error: error instanceof Error ? error.message : String(error) });
      return NextResponse.json(
        { error: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    // Validate update structure with Zod
    const updateResult = TelegramUpdateSchema.safeParse(rawUpdate);
    if (!updateResult.success) {
      console.error('❌ Telegram Webhook: Invalid update structure:', updateResult.error.format());
      monitor.recordSecurityEvent('INVALID_UPDATE_STRUCTURE', { 
        ip: clientIP, 
        errors: updateResult.error.format() 
      });
      return NextResponse.json(
        { error: 'Invalid update structure' },
        { status: 400 }
      );
    }

    const update = updateResult.data;
    updateId = update.update_id;

    // Security check: reject updates from bots
    const isFromBot = update.message?.from?.is_bot || update.callback_query?.from?.is_bot;
    if (isFromBot) {
      console.warn('⚠️ Telegram Webhook: Rejected update from bot user');
      monitor.recordSecurityEvent('BOT_USER_REJECTED', { ip: clientIP, updateId });
      return NextResponse.json({ ok: true }); // Return OK but don't process
    }

    // Security check: only allow private chats for now
    if (update.message && update.message.chat.type !== 'private') {
      console.warn('⚠️ Telegram Webhook: Rejected update from non-private chat:', update.message.chat.type);
      monitor.recordSecurityEvent('NON_PRIVATE_CHAT_REJECTED', { 
        ip: clientIP, 
        updateId, 
        chatType: update.message.chat.type 
      });
      return NextResponse.json({ ok: true }); // Return OK but don't process
    }

    console.log('✅ Telegram Webhook: Valid update received:', {
      updateId: update.update_id,
      messageId: update.message?.message_id,
      chatId: update.message?.chat?.id || update.callback_query?.message?.chat?.id,
      from: update.message?.from?.username || update.callback_query?.from?.username,
      chatType: update.message?.chat?.type,
      securityLevel: securityResult.securityInfo.securityLevel,
      processingTime: Date.now() - startTime
    });

    // Process the update with bot service
    const bot = getBotService();
    await Promise.race([
      bot.processUpdate(update),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Webhook processing timeout')), 25000) // 25s timeout
      )
    ]);

    // Log successful processing
    const processingTime = Date.now() - startTime;
    console.log('✅ Telegram Webhook: Update processed successfully:', {
      updateId,
      processingTime: `${processingTime}ms`,
      securityLevel: securityResult.securityInfo.securityLevel
    });

    // Record successful processing event
    monitor.recordSecurityEvent('UPDATE_PROCESSED', {
      updateId,
      processingTime,
      securityLevel: securityResult.securityInfo.securityLevel
    });

    // Return success response
    return NextResponse.json({ ok: true });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    // Enhanced error logging
    console.error('❌ Telegram Webhook: Error processing update:', {
      updateId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTime: `${processingTime}ms`
    });
    
    // Return error response but don't expose internal details
    return NextResponse.json(
      { 
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * Handle GET requests (for webhook verification or health checks)
 */
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const action = url.searchParams.get('action');

    if (action === 'health') {
      const monitor = TelegramSecurityMonitor.getInstance();
      const securityStats = monitor.getSecurityStats();
      
      return NextResponse.json({
        status: 'healthy',
        service: 'telegram-webhook',
        timestamp: new Date().toISOString(),
        botConfigured: !!process.env.TELEGRAM_BOT_TOKEN,
        security: {
          ...securityStats,
          webhookSecretConfigured: !!process.env.TELEGRAM_WEBHOOK_SECRET
        }
      });
    }

    if (action === 'setup') {
      // Setup webhook endpoint
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      if (!botToken) {
        return NextResponse.json(
          { error: 'Bot token not configured' },
          { status: 500 }
        );
      }

      const webhookUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://buddychip.app'}/api/telegram/webhook`;
      
      try {
        const bot = getBotService();
        await bot.setWebhook(webhookUrl);
        
        return NextResponse.json({
          success: true,
          message: 'Webhook set successfully',
          webhookUrl,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('❌ Telegram Webhook: Error setting webhook:', error);
        return NextResponse.json(
          { error: 'Failed to set webhook' },
          { status: 500 }
        );
      }
    }

    // Default response for GET requests
    return NextResponse.json({
      service: 'telegram-webhook',
      message: 'Webhook endpoint is active',
      timestamp: new Date().toISOString(),
      availableActions: ['health', 'setup']
    });

  } catch (error) {
    console.error('❌ Telegram Webhook: Error in GET handler:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_APP_URL || 'https://buddychip.app',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-telegram-bot-api-secret-token',
    },
  });
}
