"use client";

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { LandingLayout } from "@/components/landing";

export default function LandingPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();

  // <PERSON>le redirect in useEffect to avoid hydration mismatch
  useEffect(() => {
    if (isLoaded && user) {
      console.log("🔄 LandingPage: Redirecting authenticated user to dashboard");
      router.push("/dashboard");
    }
  }, [isLoaded, user, router]);

  // Always render the landing layout to avoid hydration mismatch
  // The redirect will happen after hydration
  return <LandingLayout />;
}
