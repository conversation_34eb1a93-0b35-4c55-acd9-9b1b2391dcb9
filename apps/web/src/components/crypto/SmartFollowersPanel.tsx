"use client";

import {
  ExternalLink,
  Loader2,
  Plus,
  Search,
  Star,
  TrendingUp,
  Users,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { trpc } from "@/utils/trpc";

interface DiscoveredAccount {
  handle: string;
  displayName: string;
  avatarUrl?: string;
  followerCount?: number;
  smartScore?: number;
  influence?: number;
  sector?: string;
  source: string;
  verified?: boolean;
}

export default function SmartFollowersPanel() {
  const [seedAccount, setSeedAccount] = useState("");
  const [targetSector, setTargetSector] = useState("");
  const [discoveredAccounts, setDiscoveredAccounts] = useState<
    DiscoveredAccount[]
  >([]);
  const [isDiscovering, setIsDiscovering] = useState(false);

  // tRPC queries and mutations
  const utils = trpc.useUtils();
  const { data: sectors } = trpc.crypto.getSectors.useQuery();
  const discoverAccountsMutation =
    trpc.accounts.discoverSmartAccounts.useMutation();
  const addAccountMutation = trpc.accounts.add.useMutation();

  const handleDiscoverAccounts = async () => {
    if (!seedAccount.trim() && !targetSector) {
      toast.error(
        "Please provide either a seed account or select a target sector"
      );
      return;
    }

    setIsDiscovering(true);
    try {
      const result = await discoverAccountsMutation.mutateAsync({
        seedAccount: seedAccount.trim() || undefined,
        targetSector: targetSector || undefined,
        limit: 10,
      });

      if (result.success && result.accounts.length > 0) {
        setDiscoveredAccounts(result.accounts);
        toast.success(result.suggestions.message);
      } else {
        setDiscoveredAccounts([]);
        toast.warning("No new accounts discovered with current criteria");
      }
    } catch (error) {
      console.error("Discovery error:", error);
      toast.error("Failed to discover accounts. Please try again.");
    } finally {
      setIsDiscovering(false);
    }
  };

  const handleAddAccount = async (account: DiscoveredAccount) => {
    try {
      const result = await addAccountMutation.mutateAsync({
        handle: account.handle,
        syncSettings: {
          syncMentions: true,
          syncUserTweets: false,
          syncReplies: false,
          syncRetweets: false,
        },
      });

      if (result.success) {
        toast.success(`Added @${account.handle} to monitoring`);
        // Remove from discovered accounts
        setDiscoveredAccounts((prev) =>
          prev.filter((acc) => acc.handle !== account.handle)
        );
        // Invalidate accounts cache
        utils.accounts.getMonitored.invalidate();
      }
    } catch (error) {
      console.error("Add account error:", error);
      const errorMessage = (error as any)?.message || "Failed to add account";
      toast.error(errorMessage);
    }
  };

  const getInfluenceColor = (score?: number) => {
    if (!score) return "text-gray-500";
    if (score > 80) return "text-green-600";
    if (score > 60) return "text-blue-600";
    if (score > 40) return "text-yellow-600";
    return "text-gray-500";
  };

  const formatFollowerCount = (count?: number) => {
    if (!count) return "N/A";
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  return (
    <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-app-headline flex items-center gap-2">
          <Users className="w-5 h-5 text-app-main" />
          Smart Account Discovery
        </CardTitle>
        <p className="text-sm text-app-headline/60">
          Discover high-influence accounts to monitor using smart followers
          analysis and sector trending
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Discovery Controls */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Seed Account Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-app-headline">
                Seed Account (Optional)
              </label>
              <Input
                placeholder="@elonmusk, @vitalik, etc."
                value={seedAccount}
                onChange={(e) => setSeedAccount(e.target.value)}
                className="bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline/50"
              />
              <p className="text-xs text-app-headline/50">
                Use an account you admire to find their influential followers
              </p>
            </div>

            {/* Target Sector Selector */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-app-headline">
                Target Sector (Optional)
              </label>
              <select
                value={targetSector}
                onChange={(e) => setTargetSector(e.target.value)}
                className="w-full px-3 py-2 text-sm border border-app-stroke rounded-md bg-app-background text-app-headline focus:border-app-main focus:outline-none"
              >
                <option value="">All Sectors</option>
                {sectors?.data?.map((sector: any) => (
                  <option key={sector.id} value={sector.slug}>
                    {sector.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-app-headline/50">
                Focus discovery on trending projects in specific sectors
              </p>
            </div>
          </div>

          {/* Discovery Button */}
          <Button
            onClick={handleDiscoverAccounts}
            disabled={isDiscovering || (!seedAccount.trim() && !targetSector)}
            className="w-full bg-app-main text-app-secondary hover:bg-app-highlight disabled:bg-app-main/50"
          >
            {isDiscovering ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Discovering Accounts...
              </>
            ) : (
              <>
                <Search className="w-4 h-4 mr-2" />
                Discover Smart Accounts
              </>
            )}
          </Button>
        </div>

        {/* Discovery Results */}
        {discoveredAccounts.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-app-headline">
                Discovered Accounts ({discoveredAccounts.length})
              </h4>
              <Badge variant="outline" className="text-xs text-app-headline/60">
                High Influence
              </Badge>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {discoveredAccounts.map((account) => (
                <div
                  key={account.handle}
                  className="p-4 bg-app-background rounded-lg border border-app-stroke/30 hover:border-app-stroke/60 transition-colors"
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex items-start gap-3 flex-1 min-w-0">
                      <Avatar className="h-10 w-10 flex-shrink-0">
                        <AvatarImage
                          src={
                            account.avatarUrl ||
                            `https://unavatar.io/twitter/${account.handle}`
                          }
                          alt={account.handle}
                        />
                        <AvatarFallback className="bg-app-main/10 text-app-main text-sm font-medium">
                          {account.handle.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="text-sm font-medium text-app-headline truncate">
                            {account.displayName || account.handle}
                          </h5>
                          {account.verified && (
                            <Star className="w-3 h-3 text-blue-500 flex-shrink-0" />
                          )}
                        </div>

                        <p className="text-xs text-app-headline/60 mb-2">
                          @{account.handle}
                        </p>

                        {/* Metrics Row */}
                        <div className="flex items-center gap-3 text-xs">
                          {account.followerCount && (
                            <span className="text-app-headline/60">
                              {formatFollowerCount(account.followerCount)}{" "}
                              followers
                            </span>
                          )}

                          {(account.smartScore || account.influence) && (
                            <div className="flex items-center gap-1">
                              <TrendingUp className="w-3 h-3 text-app-main" />
                              <span
                                className={`font-medium ${getInfluenceColor(account.smartScore || account.influence)}`}
                              >
                                {(
                                  account.smartScore || account.influence
                                )?.toFixed(0)}{" "}
                                influence
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Sector & Source Tags */}
                        <div className="flex items-center gap-2 mt-2">
                          {account.sector && (
                            <Badge
                              variant="outline"
                              className="text-xs px-1 py-0 text-app-headline/60 border-app-stroke"
                            >
                              {account.sector}
                            </Badge>
                          )}
                          <Badge
                            variant="outline"
                            className="text-xs px-1 py-0 text-app-main/70 border-app-main/30"
                          >
                            {account.source === "smart_followers"
                              ? "Smart Follower"
                              : "Trending"}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-2 flex-shrink-0">
                      <Button
                        size="sm"
                        onClick={() => handleAddAccount(account)}
                        disabled={addAccountMutation.isPending}
                        className="bg-app-main text-app-secondary hover:bg-app-highlight text-xs px-2 py-1 min-h-[32px]"
                      >
                        {addAccountMutation.isPending ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <>
                            <Plus className="w-3 h-3 mr-1" />
                            Add
                          </>
                        )}
                      </Button>

                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() =>
                          window.open(
                            `https://x.com/${account.handle}`,
                            "_blank"
                          )
                        }
                        className="text-app-headline/50 hover:text-app-main p-1 min-h-[32px] min-w-[32px]"
                      >
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {discoveredAccounts.length === 0 && !isDiscovering && (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-app-main/30 mx-auto mb-3" />
            <p className="text-sm text-app-headline/60 mb-2">
              Ready to discover influential accounts?
            </p>
            <p className="text-xs text-app-headline/40 max-w-md mx-auto">
              Enter a seed account (someone you follow) or select a crypto
              sector to find high-influence accounts worth monitoring.
            </p>
          </div>
        )}

        {/* Tips */}
        <div className="bg-app-main/5 border border-app-main/20 rounded-lg p-4">
          <h5 className="text-sm font-medium text-app-headline mb-2">
            💡 Discovery Tips
          </h5>
          <ul className="text-xs text-app-headline/70 space-y-1">
            <li>
              • Use seed accounts you respect to find their influential
              followers
            </li>
            <li>• Target specific sectors for more focused recommendations</li>
            <li>
              • Higher influence scores indicate stronger social media presence
            </li>
            <li>
              • Smart followers are identified by engagement quality, not just
              follower count
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
