"use client";

import { useUser } from "@clerk/nextjs";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { RiTwitterXLine } from "react-icons/ri";
import Logo from "@/components/logo";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import IconButton from "../atoms/icon-button";

export function Navigation() {
  const { user, isLoaded } = useUser();

  console.log(
    "🔍 Navigation: Rendering with user:",
    user ? "authenticated" : "not authenticated",
    "isLoaded:",
    isLoaded
  );

  return (
    <nav className="w-full z-50 flex justify-between items-center px-3 sm:px-4 md:px-6 h-[60px] sm:h-[70px] md:h-[104px] bg-app-background">
      <div className="flex items-center">
        <Logo
          size={160}
          href="/"
          showText={true}
          forceVersion="light"
          className="w-[120px] sm:w-[140px] md:w-[160px] h-auto"
        />
      </div>

      <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
        {/* Social Icons */}
        <IconButton
          icon={RiTwitterXLine}
          onClick={() => window.open("https://x.com/BuddychipAI", "_blank")}
          className="hover:bg-app-main hover:text-app-secondary w-10 h-10 sm:w-11 sm:h-11 md:w-12 md:h-12 min-w-[44px] min-h-[44px]"
        />

        {/* Show loading state or content based on isLoaded to prevent hydration mismatch */}
        {!isLoaded && (
          // Loading state - show placeholder buttons to maintain layout
          <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
            <div className="w-16 h-[44px] bg-app-stroke/20 rounded animate-pulse" />
            <div className="w-20 h-[44px] bg-app-stroke/20 rounded animate-pulse" />
          </div>
        )}

        {isLoaded && !user && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={() =>
                window.open("https://t.me/+4n7CEX8GKApkMmQ0", "_blank")
              }
              className="border border-app-stroke bg-transparent text-app-headline hover:bg-app-stroke hover:text-app-secondary min-h-[44px] px-3 sm:px-4 text-xs sm:text-sm"
            >
              <span className="hidden sm:inline">Access Beta!</span>
              <span className="sm:hidden">Beta</span>
            </Button>
            <Link href="/sign-in">
              <Button
                size="sm"
                className="bg-app-main text-app-secondary hover:bg-app-highlight min-h-[44px] px-3 sm:px-4 text-xs sm:text-sm"
              >
                Sign In
              </Button>
            </Link>
          </>
        )}

        {isLoaded && user && (
          <>
            <Link href="/dashboard">
              <Button
                size="sm"
                className="bg-app-main text-app-secondary hover:bg-app-highlight flex items-center space-x-1 min-h-[44px] px-3 sm:px-4 text-xs sm:text-sm"
              >
                <span className="hidden sm:inline">Dashboard</span>
                <span className="sm:hidden">App</span>
                <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative min-h-[44px] flex items-center gap-1 sm:gap-2 hover:bg-app-stroke px-2 sm:px-3"
                >
                  <Avatar className="h-8 w-8 sm:h-9 sm:w-9">
                    <AvatarImage
                      src={user?.imageUrl}
                      alt={user?.fullName || "User"}
                    />
                    <AvatarFallback className="bg-app-main text-app-secondary text-sm">
                      {(user?.fullName || user?.firstName || "U")
                        .charAt(0)
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left hidden sm:block">
                    <p className="text-xs sm:text-sm font-medium text-app-headline truncate max-w-[80px] sm:max-w-[120px]">
                      {user?.fullName || user?.firstName || "User"}
                    </p>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-56 sm:w-64 bg-app-card border-app-stroke mt-2 mr-2 sm:mr-0"
                sideOffset={8}
              >
                <div className="flex items-center gap-3 p-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src={user?.imageUrl}
                      alt={user?.fullName || "User"}
                    />
                    <AvatarFallback className="bg-app-main text-app-secondary">
                      {(user?.fullName || user?.firstName || "U")
                        .charAt(0)
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-app-headline truncate">
                      {user?.fullName || user?.firstName || "User"}
                    </p>
                    <p className="text-xs text-app-headline opacity-70 truncate">
                      {user?.primaryEmailAddress?.emailAddress}
                    </p>
                  </div>
                </div>

                <DropdownMenuSeparator />

                <Link href="/sign-out">
                  <DropdownMenuItem className="text-app-headline hover:bg-app-main/10 cursor-pointer min-h-[44px] flex items-center">
                    Sign Out
                  </DropdownMenuItem>
                </Link>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </div>
    </nav>
  );
}
