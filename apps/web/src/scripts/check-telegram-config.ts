#!/usr/bin/env tsx

/**
 * Telegram Configuration Checker
 * 
 * Validates environment variables and configuration for Telegram bot
 */

import { validateTelegramConfig, checkTelegramEnvironment } from '../lib/telegram-utils';

function main() {
  console.log('🤖 Telegram Bot Configuration Checker\n');

  // Check environment variables
  console.log('📋 Checking Environment Variables...');
  const envCheck = checkTelegramEnvironment();
  
  if (envCheck.ready) {
    console.log('✅ All required environment variables are set');
  } else {
    console.log('❌ Missing required environment variables:');
    envCheck.missing.forEach(key => {
      console.log(`   - ${key}`);
    });
  }

  if (envCheck.optional.length > 0) {
    console.log('⚠️  Optional environment variables not set:');
    envCheck.optional.forEach(key => {
      console.log(`   - ${key}`);
    });
  }

  console.log();

  // Check Telegram configuration
  console.log('⚙️  Checking Telegram Configuration...');
  const config = validateTelegramConfig();
  
  if (config.isValid) {
    console.log('✅ Telegram configuration is valid');
  } else {
    console.log('❌ Telegram configuration errors:');
    config.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }

  if (config.warnings.length > 0) {
    console.log('⚠️  Telegram configuration warnings:');
    config.warnings.forEach(warning => {
      console.log(`   - ${warning}`);
    });
  }

  console.log();

  // Check bot token format
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  if (botToken) {
    console.log('🔑 Checking Bot Token Format...');
    
    // Basic token format validation
    const tokenRegex = /^\d+:[A-Za-z0-9_-]+$/;
    if (tokenRegex.test(botToken)) {
      console.log('✅ Bot token format appears valid');
      
      // Extract bot ID from token
      const botId = botToken.split(':')[0];
      console.log(`   Bot ID: ${botId}`);
    } else {
      console.log('❌ Bot token format appears invalid');
      console.log('   Expected format: 123456789:ABCdefGHIjklMNOpqrSTUvwxyz');
    }
  }

  console.log();

  // Check webhook URL
  const appUrl = process.env.NEXT_PUBLIC_APP_URL;
  if (appUrl) {
    console.log('🌐 Checking Webhook URL...');
    
    const webhookUrl = `${appUrl}/api/telegram/webhook`;
    console.log(`   Webhook URL: ${webhookUrl}`);
    
    // Basic URL validation
    try {
      const url = new URL(webhookUrl);
      if (url.protocol === 'https:') {
        console.log('✅ Webhook URL uses HTTPS (required by Telegram)');
      } else {
        console.log('❌ Webhook URL must use HTTPS for production');
      }
    } catch (error) {
      console.log('❌ Invalid webhook URL format');
    }
  }

  console.log();

  // Database check
  console.log('🗄️  Checking Database Configuration...');
  
  const databaseUrl = process.env.DATABASE_URL;
  if (databaseUrl) {
    console.log('✅ DATABASE_URL is set');
    
    // Check if it's a PostgreSQL URL
    if (databaseUrl.startsWith('postgresql://') || databaseUrl.startsWith('postgres://')) {
      console.log('✅ Using PostgreSQL (required for Telegram tables)');
    } else {
      console.log('⚠️  Database URL doesn\'t appear to be PostgreSQL');
    }
  } else {
    console.log('❌ DATABASE_URL is not set');
  }

  console.log();

  // Summary
  console.log('📊 Configuration Summary:');
  console.log(`   Environment Ready: ${envCheck.ready ? '✅' : '❌'}`);
  console.log(`   Config Valid: ${config.isValid ? '✅' : '❌'}`);
  console.log(`   Bot Token: ${botToken ? '✅' : '❌'}`);
  console.log(`   Webhook URL: ${appUrl ? '✅' : '❌'}`);
  console.log(`   Database: ${databaseUrl ? '✅' : '❌'}`);

  console.log();

  if (envCheck.ready && config.isValid) {
    console.log('🎉 Telegram bot is ready for deployment!');
    console.log();
    console.log('Next steps:');
    console.log('1. Deploy your application');
    console.log('2. Set up the webhook: GET /api/telegram/webhook?action=setup');
    console.log('3. Test the bot by sending /start');
  } else {
    console.log('🔧 Please fix the configuration issues above before deploying.');
  }

  console.log();

  // Exit with appropriate code
  process.exit(envCheck.ready && config.isValid ? 0 : 1);
}

if (require.main === module) {
  main();
}
