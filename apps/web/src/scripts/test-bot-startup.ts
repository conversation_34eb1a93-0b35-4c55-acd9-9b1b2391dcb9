#!/usr/bin/env bun

/**
 * Test if the Telegram bot starts automatically
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(__dirname, '../.env') });

// Set NODE_ENV to development to trigger auto-start
process.env.NODE_ENV = 'development';

console.log('🧪 Testing Telegram Bot Auto-Startup...\n');

console.log('📋 Environment Check:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`   TELEGRAM_BOT_TOKEN: ${process.env.TELEGRAM_BOT_TOKEN ? '✅ Set' : '❌ Missing'}`);
console.log();

console.log('🚀 Importing telegram-startup module...');

// Import the startup module (this should trigger auto-start)
import { getBotService, isBotInitialized } from '../lib/telegram-startup';

console.log('✅ Module imported');

// Wait a moment for initialization
await new Promise(resolve => setTimeout(resolve, 1000));

console.log('\n📊 Bot Status:');
console.log(`   Initialized: ${isBotInitialized() ? '✅ Yes' : '❌ No'}`);
console.log(`   Service exists: ${getBotService() ? '✅ Yes' : '❌ No'}`);

if (isBotInitialized()) {
  console.log('\n🎉 Success! The Telegram bot should now be listening for messages.');
  console.log('\nNext steps:');
  console.log('1. Start your development server: bun dev');
  console.log('2. Send /start to @Benji_BuddyChip_Bot on Telegram');
  console.log('3. Check the console logs for bot activity');
} else {
  console.log('\n❌ Bot failed to initialize. Check the logs above for errors.');
}

console.log('\n🔍 Troubleshooting:');
console.log('- Make sure TELEGRAM_BOT_TOKEN is set in your .env file');
console.log('- Check if you have internet connectivity');
console.log('- Wait for rate limits to expire if you see 429 errors');

process.exit(0);
