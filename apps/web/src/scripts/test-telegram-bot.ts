#!/usr/bin/env tsx

/**
 * Test Telegram Bot Configuration and Connectivity
 * 
 * This script tests if the Telegram bot is properly configured and can respond to messages.
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(__dirname, '../.env') });

import { initializeTelegramBot, checkTelegramEnvironment } from '../lib/telegram-utils';

async function testTelegramBot() {
  console.log('🧪 Testing Telegram Bot Configuration...\n');

  // Step 1: Check environment variables
  console.log('📋 Step 1: Checking environment variables...');
  const envCheck = checkTelegramEnvironment();
  
  console.log('✅ Required variables:', envCheck.missing.length === 0 ? 'All present' : `Missing: ${envCheck.missing.join(', ')}`);
  console.log('⚠️ Optional variables:', envCheck.optional.length === 0 ? 'All present' : `Missing: ${envCheck.optional.join(', ')}`);
  
  if (!envCheck.ready) {
    console.error('❌ Missing required environment variables. Please check your .env file.');
    process.exit(1);
  }

  // Step 2: Initialize bot service
  console.log('\n🤖 Step 2: Initializing bot service...');
  const botService = initializeTelegramBot();
  
  if (!botService) {
    console.error('❌ Failed to initialize bot service');
    process.exit(1);
  }
  
  console.log('✅ Bot service initialized successfully');

  // Step 3: Test bot token validity
  console.log('\n🔑 Step 3: Testing bot token...');
  try {
    // Create a direct bot instance to test the token
    const TelegramBot = (await import('node-telegram-bot-api')).default;
    const testBot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN!, { polling: false });

    const botInfo = await testBot.getMe();
    console.log('✅ Bot token is valid');
    console.log(`   Bot name: ${botInfo.first_name}`);
    console.log(`   Bot username: @${botInfo.username}`);
    console.log(`   Bot ID: ${botInfo.id}`);

    // Clean up test bot
    await testBot.close();
  } catch (error) {
    console.error('❌ Bot token is invalid or bot is unreachable:', error);
    process.exit(1);
  }

  // Step 4: Check webhook status (if in production)
  if (process.env.NODE_ENV === 'production') {
    console.log('\n🌐 Step 4: Checking webhook status...');
    try {
      const TelegramBot = (await import('node-telegram-bot-api')).default;
      const testBot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN!, { polling: false });

      const webhookInfo = await testBot.getWebHookInfo();
      console.log('📡 Webhook info:');
      console.log(`   URL: ${webhookInfo.url || 'Not set'}`);
      console.log(`   Pending updates: ${webhookInfo.pending_update_count}`);
      console.log(`   Last error: ${webhookInfo.last_error_message || 'None'}`);

      if (!webhookInfo.url) {
        console.log('⚠️ Webhook not set. You need to set it up for production.');
        console.log('   Run: curl "https://buddychip.app/api/telegram/webhook?action=setup"');
      }

      await testBot.close();
    } catch (error) {
      console.error('❌ Error checking webhook:', error);
    }
  } else {
    console.log('\n🔄 Step 4: Development mode - using polling');
    console.log('✅ Bot should be listening for messages via polling');
  }

  // Step 5: Test database connectivity
  console.log('\n💾 Step 5: Testing database connectivity...');
  try {
    const { prisma } = await import('../lib/db-utils');
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Test if Telegram tables exist
    const telegramUserCount = await prisma.telegramUser.count();
    console.log(`   Telegram users in database: ${telegramUserCount}`);
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }

  console.log('\n🎉 Telegram bot test completed!');
  console.log('\nNext steps:');
  console.log('1. Make sure your development server is running: bun dev');
  console.log('2. Send /start to your bot on Telegram');
  console.log('3. Check the console logs for bot activity');
  
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the test
testTelegramBot().catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
