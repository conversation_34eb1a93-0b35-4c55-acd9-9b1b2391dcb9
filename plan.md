# BuddyChip Development Plans

## Subscription System Restructure Plan

### ✅ Analysis Complete
- [x] Analyzed current database state
- [x] Identified two team plans to merge
- [x] Found pricing inconsistencies between code and database
- [x] Reviewed current UI components and billing system

### ✅ Database Changes Complete
- [x] Removed duplicate team plan ($299 one)
- [x] Added free tier plan and features
- [x] Updated team plan to unified structure
- [x] Updated all plan features with correct limits
- [x] Verified database state is correct

### ✅ New Components Created
- [x] `apps/web/src/components/upgrade-prompt.tsx` - Smart upgrade prompts
- [x] `apps/web/src/components/feature-gate.tsx` - Feature limiting with upgrade options
- [x] `apps/web/src/components/free-tier-onboarding.tsx` - Welcome experience for free users
- [x] `test/subscription-test.ts` - Comprehensive subscription system test

### 🎯 Goals
1. Merge two team plans into one comprehensive team plan
2. Add a free tier to attract new users
3. Create logical feature progression across tiers
4. Ensure consistency between database, code, and UI

### 📊 New Subscription Tier Structure

#### Free Tier (New)
- **Price**: $0/month
- **Target**: New users trying the platform
- **Features**:
  - 50 AI calls/month
  - 5 image generations/month
  - 1 monitored account
  - 100 mentions/month
  - 25 mentions per sync
  - 50 max total mentions
  - 0.5GB storage
  - 1 team member
  - 10 Cookie API calls/month
  - Basic support (community)

#### Reply Guy - $9/month
- **Target**: Individual users getting started
- **Features**:
  - 1,000 AI calls/month
  - 20 image generations/month
  - 3 monitored accounts
  - 1,000 mentions/month
  - 25 mentions per sync
  - 100 max total mentions
  - 1GB storage
  - 1 team member
  - 50 Cookie API calls/month
  - Email support

#### Reply God - $29/month
- **Target**: Power users and small teams
- **Features**:
  - 5,000 AI calls/month
  - 100 image generations/month
  - 10 monitored accounts
  - 5,000 mentions/month
  - 100 mentions per sync
  - 500 max total mentions
  - 5GB storage
  - 3 team members
  - 200 Cookie API calls/month
  - Priority support
  - Custom personas

#### Team - $99/month
- **Target**: Teams and organizations
- **Features**:
  - Unlimited AI calls
  - Unlimited image generations
  - 50 monitored accounts
  - Unlimited mentions/month
  - 200 mentions per sync
  - 2,000 max total mentions
  - 20GB storage
  - 10 team members
  - Unlimited Cookie API calls
  - Dedicated support
  - Team collaboration
  - Admin dashboard
  - Custom integrations

---

## Telegram Bot Integration Plan

### Overview
Implement a full-featured Telegram bot that provides complete access to the Benji AI agent capabilities, including Twitter link processing and all existing tools.

### Phase 1: Core Infrastructure ✅
- [x] Plan created
- [x] Install Telegram bot dependencies
- [x] Create core Telegram bot service
- [x] Update database schema for Telegram users
- [x] Create authentication system

### Phase 2: API Routes & Webhooks ✅
- [x] Create Telegram webhook endpoint
- [x] Create authentication API routes
- [x] Extend tRPC router with Telegram procedures
- [x] Set up webhook with Telegram API

### Phase 3: Benji Agent Integration ✅
- [x] Create TelegramBenjiAgent wrapper class
- [x] Implement message formatting for Telegram constraints
- [x] Add conversation context management
- [x] Integrate all existing tools (xAI, Exa, image generation)

### Phase 4: Twitter Link Processing ✅
- [x] Implement Twitter URL detection in messages
- [x] Integrate with existing quick reply functionality
- [x] Format Twitter responses for Telegram
- [x] Add enhanced response support

### Phase 5: User Experience ✅
- [x] Implement command system (/start, /help, /settings, /status)
- [x] Add conversation management
- [x] Integrate rate limiting
- [x] Add user preferences and model selection

### Phase 6: Testing & Deployment ✅
- [x] Create comprehensive tests
- [x] Create deployment documentation
- [x] Create configuration checker script
- [x] Ready for webhook setup on buddychip.app domain
- [x] Ready for deployment and user testing

### Technical Requirements
- Domain: buddychip.app for webhook endpoints
- All existing Benji tools accessible via Telegram
- Twitter link processing identical to dashboard quick reply
- User authentication and account linking
- Rate limiting and subscription plan integration
- Conversation state management

### Key Features
1. **Full Benji Agent Access**: All tools and capabilities available
2. **Twitter Integration**: Send Twitter links for instant AI replies
3. **Multi-turn Conversations**: Context-aware discussions
4. **User Management**: Account linking and preferences
5. **Rate Limiting**: Subscription plan enforcement
6. **Rich Responses**: Text, images, and formatted content
