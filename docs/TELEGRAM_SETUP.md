# Telegram Bot Integration Setup

This guide explains how to set up and deploy the BuddyChip Telegram bot that provides full access to the Benji AI agent.

## Overview

The Telegram bot integration allows users to:
- Access all Benji AI agent capabilities via Telegram
- Send Twitter/X links for instant AI-generated replies
- Have conversations with AI using all available tools
- Generate images, search the web, and get crypto insights
- Link their BuddyChip account for personalized responses

## Prerequisites

1. **BuddyChip Application**: Fully deployed and running
2. **Telegram Bot Token**: From @<PERSON>t<PERSON>ather on Telegram
3. **Domain Access**: buddychip.app for webhook endpoints
4. **Database**: Prisma schema updated with Telegram tables

## Step 1: Create Telegram Bot

1. **Start conversation with @<PERSON>tFather** on Telegram
2. **Create new bot**:
   ```
   /newbot
   ```
3. **Choose bot name**: `BuddyChip AI` (or your preferred name)
4. **Choose username**: `buddychip_ai_bot` (must end with 'bot')
5. **Save the bot token** provided by <PERSON><PERSON><PERSON>ather

## Step 2: Configure Environment Variables

Add these environment variables to your deployment:

```env
# Required
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather

# Recommended for security
TELEGRAM_WEBHOOK_SECRET=your_random_secret_string

# Required for production webhook
NEXT_PUBLIC_APP_URL=https://buddychip.app
```

## Step 3: Database Migration

Run the database migration to create Telegram tables:

```bash
cd apps/web
npm run db:migrate
```

This creates:
- `telegram_users` table for Telegram user data
- `telegram_sessions` table for conversation context

## Step 4: Deploy Application

Deploy your updated application with the new Telegram integration code.

## Step 5: Set Up Webhook

After deployment, set up the webhook by visiting:

```
https://buddychip.app/api/telegram/webhook?action=setup
```

This will:
- Configure the webhook URL with Telegram
- Verify the bot token
- Enable message processing

## Step 6: Test the Bot

1. **Find your bot** on Telegram using the username you created
2. **Start conversation** with `/start`
3. **Test basic functionality**:
   - Send `/help` for command list
   - Send a Twitter/X link to test quick reply
   - Ask a general question to test AI conversation

## Bot Commands

| Command | Description |
|---------|-------------|
| `/start` | Welcome message and setup |
| `/help` | Show all available commands |
| `/settings` | Account settings and linking |
| `/status` | Usage limits and account status |

## Features

### 🐦 Twitter Integration
- Send any Twitter/X link
- Get instant AI-generated replies
- Enhanced responses with o3 model
- Copy-paste ready format

### 💬 AI Conversations
- Natural language interactions
- Context-aware responses
- Multi-turn conversations
- All Benji tools available

### 🎨 Image Generation
- DALL-E 3 powered images
- Detailed prompt support
- High-quality outputs

### 🔍 Web Search
- Real-time information
- xAI and Exa search integration
- Current events and trends

## Account Linking

Users can link their BuddyChip accounts to access premium features:

1. **User visits** `/settings` in Telegram
2. **Bot provides** unique link code
3. **User enters code** in BuddyChip dashboard
4. **Accounts are linked** automatically

## Rate Limiting

The bot respects BuddyChip subscription plans:
- **Free users**: Limited AI calls per month
- **Premium users**: Higher limits or unlimited
- **Rate limit messages**: Shown when limits exceeded

## Security Features

- **Webhook signature validation** (if secret configured)
- **User authentication** via account linking
- **Rate limiting** per subscription plan
- **Input validation** for all requests
- **Error handling** with user-friendly messages

## Monitoring and Logs

The bot provides comprehensive logging:

```bash
# View Telegram bot logs
grep "Telegram" /var/log/your-app.log

# Monitor webhook health
curl https://buddychip.app/api/telegram/webhook?action=health
```

## Troubleshooting

### Bot Not Responding
1. Check webhook setup: `/api/telegram/webhook?action=health`
2. Verify bot token in environment variables
3. Check application logs for errors

### Webhook Issues
1. Ensure HTTPS is enabled on your domain
2. Verify webhook URL is accessible
3. Check Telegram webhook status with BotFather

### Database Errors
1. Verify Telegram tables exist
2. Check database connection
3. Run migration if needed

### Rate Limiting
1. Check user's subscription plan
2. Verify rate limiting configuration
3. Monitor usage logs

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/telegram/webhook` | POST | Process Telegram updates |
| `/api/telegram/webhook?action=health` | GET | Health check |
| `/api/telegram/webhook?action=setup` | GET | Setup webhook |
| `/api/telegram/auth` | POST | Link account |
| `/api/telegram/auth` | DELETE | Unlink account |
| `/api/telegram/auth` | GET | Get linked account |

## tRPC Procedures

| Procedure | Description |
|-----------|-------------|
| `telegram.getLinkedAccount` | Get linked Telegram account |
| `telegram.linkAccount` | Link Telegram to BuddyChip |
| `telegram.unlinkAccount` | Unlink accounts |
| `telegram.getBotInfo` | Get bot configuration |
| `telegram.testIntegration` | Test integration |

## Development vs Production

### Development
- Uses polling instead of webhooks
- Local testing with ngrok recommended
- Debug logging enabled

### Production
- Uses webhooks for better performance
- HTTPS required for webhook
- Production logging levels

## Performance Considerations

- **Message splitting**: Long responses automatically split
- **Context management**: Conversation history limited to 20 messages
- **Session expiry**: Sessions expire after 24 hours
- **Rate limiting**: Prevents abuse and manages costs

## Future Enhancements

Planned features for future releases:
- Voice message support
- File upload handling
- Group chat integration
- Advanced analytics
- Custom personality selection
- Scheduled messages

## Support

For issues or questions:
1. Check application logs
2. Verify configuration
3. Test with simple commands
4. Contact development team with specific error messages

## Security Best Practices

1. **Keep bot token secure** - Never commit to version control
2. **Use webhook secrets** - Validate incoming requests
3. **Monitor usage** - Watch for unusual activity
4. **Regular updates** - Keep dependencies current
5. **Error handling** - Don't expose internal details
