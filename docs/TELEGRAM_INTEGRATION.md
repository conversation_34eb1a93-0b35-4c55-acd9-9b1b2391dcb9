# Telegram Bot Integration

## Overview

The BuddyChip Telegram bot provides full access to the Benji AI agent through Telegram, enabling users to:

- 🐦 **Twitter Integration**: Send Twitter/X links for instant AI-generated replies
- 💬 **AI Conversations**: Chat with <PERSON><PERSON> using all available tools
- 🎨 **Image Generation**: Create images with DALL-E 3
- 🔍 **Web Search**: Get real-time information using xAI and Exa
- 📊 **Crypto Insights**: Access crypto market data and analysis

## Architecture

### Core Components

1. **TelegramBotService** (`src/lib/telegram-bot.ts`)
   - Main bot logic and event handling
   - Command processing and message routing
   - User interaction management

2. **TelegramBenjiAgent** (`src/lib/telegram-benji-agent.ts`)
   - Specialized wrapper around BenjiAgent
   - Telegram-optimized response formatting
   - Conversation context management

3. **Telegram Authentication** (`src/lib/telegram-auth.ts`)
   - Account linking between Telegram and BuddyChip
   - Session management
   - Access control

4. **API Routes**
   - `/api/telegram/webhook` - Webhook handler
   - `/api/telegram/auth` - Authentication endpoints

5. **tRPC Router** (`src/routers/telegram.ts`)
   - Type-safe API procedures
   - Integration with existing tRPC infrastructure

### Database Schema

```sql
-- Telegram users table
CREATE TABLE telegram_users (
  id TEXT PRIMARY KEY,
  telegram_id TEXT UNIQUE NOT NULL,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  language_code TEXT,
  user_id TEXT REFERENCES users(id),
  is_active BOOLEAN DEFAULT true,
  is_blocked BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  last_active_at TIMESTAMP
);

-- Telegram sessions table
CREATE TABLE telegram_sessions (
  id TEXT PRIMARY KEY,
  telegram_user_id TEXT REFERENCES telegram_users(id),
  context JSONB,
  current_tool TEXT,
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now()
);
```

## Features

### Command System

| Command | Description | Example |
|---------|-------------|---------|
| `/start` | Welcome message and setup | `/start` |
| `/help` | Show available commands | `/help` |
| `/settings` | Account management | `/settings` |
| `/status` | Usage and limits | `/status` |

### Twitter Integration

Users can send Twitter/X links to get AI-generated replies:

```
User: https://twitter.com/user/status/123456
Bot: 🐦 AI Reply Generated

Original Tweet:
"Sample tweet content..."

Suggested Reply:
This is a thoughtful AI-generated response...

[Regenerate] [Enhance] [Copy Reply]
```

### AI Conversations

Natural language conversations with full tool access:

```
User: What's the latest news about AI?
Bot: 🔍 Searching for latest AI news...

Based on my search, here are the latest AI developments:
1. OpenAI releases new model...
2. Google announces breakthrough...
```

### Image Generation

AI-powered image creation:

```
User: Generate an image of a futuristic city
Bot: 🎨 Generating image with DALL-E 3...

[Generated image]

Image created: "Futuristic city with flying cars..."
```

## Account Linking

Users link their BuddyChip accounts to access premium features:

1. **User sends** `/settings` command
2. **Bot provides** unique link code (expires in 10 minutes)
3. **User enters code** in BuddyChip dashboard
4. **Accounts linked** automatically

```typescript
// Generate link code
const linkCode = generateTelegramLinkCode(chatId);
// Format: TG_123456789_1640995200000_abc123def456

// Link accounts
const result = await linkTelegramAccount(userId, linkCode);
```

## Rate Limiting

Respects BuddyChip subscription plans:

- **Free Plan**: Limited AI calls per month
- **Premium Plans**: Higher limits or unlimited usage
- **Rate limit messages**: Shown when limits exceeded

```typescript
// Check rate limits before processing
const rateLimit = await checkRateLimit(userId, FeatureType.AI_CALLS, 1);
if (!rateLimit.allowed) {
  await bot.sendMessage(chatId, `⚠️ AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`);
  return;
}
```

## Message Formatting

Handles Telegram's 4096 character limit:

```typescript
// Split long messages
const messages = benjiAgent.formatForTelegram(longResponse);
for (const message of messages) {
  await bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
}
```

## Conversation Context

Maintains conversation history for context-aware responses:

```typescript
// Load conversation context
const context = await loadConversationContext(telegramUserId);

// Generate response with context
const response = await benjiAgent.generateTelegramResponse(message, context);

// Save updated context
await saveConversationContext(context, userMessage, response);
```

## Security Features

1. **Webhook Signature Validation**
   ```typescript
   const isValid = validateTelegramWebhook(body, signature, botToken);
   ```

2. **Rate Limiting**
   - Per-user limits based on subscription
   - Prevents abuse and manages costs

3. **Input Validation**
   - All inputs validated with Zod schemas
   - Sanitized before processing

4. **Error Handling**
   - Graceful error messages
   - No internal details exposed

## Development Setup

1. **Install Dependencies**
   ```bash
   npm install node-telegram-bot-api @types/node-telegram-bot-api
   ```

2. **Environment Variables**
   ```env
   TELEGRAM_BOT_TOKEN=your_bot_token
   TELEGRAM_WEBHOOK_SECRET=your_secret
   NEXT_PUBLIC_APP_URL=https://buddychip.app
   ```

3. **Database Migration**
   ```bash
   npm run db:migrate
   ```

4. **Development Mode**
   ```bash
   # Uses polling instead of webhooks
   NODE_ENV=development npm run dev
   ```

## Production Deployment

1. **Set Environment Variables**
2. **Deploy Application**
3. **Set Up Webhook**
   ```bash
   curl "https://buddychip.app/api/telegram/webhook?action=setup"
   ```

## Testing

Run the test suite:

```bash
npm test test/telegram-integration.test.ts
```

Tests cover:
- Message formatting
- Command parsing
- Twitter URL detection
- Authentication flows
- Error handling

## Monitoring

### Health Check
```bash
curl "https://buddychip.app/api/telegram/webhook?action=health"
```

### Logs
```bash
# View Telegram-specific logs
grep "Telegram" /var/log/app.log

# Monitor webhook activity
tail -f /var/log/app.log | grep "📨 Telegram"
```

### Metrics
- Message processing time
- User engagement rates
- Error rates by type
- Rate limit hits

## Troubleshooting

### Common Issues

1. **Bot Not Responding**
   - Check webhook setup
   - Verify bot token
   - Check application logs

2. **Rate Limiting Issues**
   - Verify user subscription
   - Check rate limit configuration
   - Monitor usage patterns

3. **Database Errors**
   - Ensure tables exist
   - Check connection string
   - Verify migrations

### Debug Commands

```bash
# Test webhook
curl -X POST https://buddychip.app/api/telegram/webhook \
  -H "Content-Type: application/json" \
  -d '{"update_id": 1, "message": {"message_id": 1, "chat": {"id": 123}, "from": {"id": 123}, "text": "/start"}}'

# Check bot info
curl "https://buddychip.app/api/telegram/webhook?action=health"
```

## Performance Optimization

1. **Message Caching**
   - Cache frequently requested responses
   - Reduce API calls

2. **Batch Processing**
   - Process multiple updates together
   - Improve throughput

3. **Connection Pooling**
   - Reuse database connections
   - Reduce latency

## Future Enhancements

- Voice message support
- File upload handling
- Group chat integration
- Advanced analytics
- Custom personality selection
- Scheduled messages
- Multi-language support

## API Reference

### Webhook Endpoints

- `POST /api/telegram/webhook` - Process updates
- `GET /api/telegram/webhook?action=health` - Health check
- `GET /api/telegram/webhook?action=setup` - Setup webhook

### Authentication Endpoints

- `POST /api/telegram/auth` - Link account
- `DELETE /api/telegram/auth` - Unlink account
- `GET /api/telegram/auth` - Get linked account

### tRPC Procedures

- `telegram.getLinkedAccount` - Get linked account info
- `telegram.linkAccount` - Link accounts
- `telegram.unlinkAccount` - Unlink accounts
- `telegram.getBotInfo` - Get bot configuration
- `telegram.testIntegration` - Test integration
